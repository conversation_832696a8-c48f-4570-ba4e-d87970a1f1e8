{"name": "grudgebookapp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "ios:sim": "react-native run-ios --simulator=\"iPhone 16 Pro\"", "start": "react-native start", "start-re": "react-native start --reset-cache", "pod:bundle-install": "(cd ./ios && bundle install)", "pod:bundle-update": "(cd ./ios && bundle update --bundler)", "pod:install": "(cd ./ios && bundle exec pod install)", "pod:install-clean": "(cd ./ios && bundle install && bundle exec pod install --clean-install)", "pod:clean": "(cd ./ios && bundle exec pod cache clean --all)", "pod:deintegrate": "(cd ./ios && bundle exec pod deintegrate)", "pod:reset": "(cd ./ios && pnpm pod:deintegrate && pnpm pod:clean && bundle install && bundle exec pod install)", "bootsplash:gen": "react-native-bootsplash generate ./src/assets/svgs/img_appIcon.svg --platforms=android,ios --assets-output=\"src/assets/bootsplash\""}, "dependencies": {"@react-navigation/bottom-tabs": "7.4.6", "@react-navigation/native": "7.1.14", "@rn-primitives/portal": "1.3.0", "@shopify/flash-list": "2.0.0-rc.10", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "es-toolkit": "1.39.10", "lottie-react-native": "7.3.2", "nativewind": "4.1.23", "react": "19.1.0", "react-native": "0.81.0", "react-native-bootsplash": "6.3.9", "react-native-css-interop": "0.1.22", "react-native-gesture-handler": "2.28.0", "react-native-haptic-feedback": "2.3.3", "react-native-reanimated": "4.0.2", "react-native-safe-area-context": "5.6.1", "react-native-screens": "4.15.3", "react-native-worklets": "0.4.1", "react-use": "17.6.0", "tailwind-merge": "3.3.1", "zustand-x": "6.1.1"}, "devDependencies": {"@babel/core": "7.27.4", "@babel/plugin-transform-react-jsx": "7.27.1", "@babel/preset-env": "7.27.2", "@babel/runtime": "7.27.6", "@react-native-community/cli": "20.0.0", "@react-native-community/cli-platform-android": "20.0.0", "@react-native-community/cli-platform-ios": "20.0.0", "@react-native/babel-preset": "0.81.0", "@react-native/codegen": "0.81.0", "@react-native/eslint-config": "0.81.0", "@react-native/gradle-plugin": "0.81.0", "@react-native/metro-config": "0.81.0", "@react-native/typescript-config": "0.81.0", "@stylistic/eslint-plugin": "3.1.0", "@types/jest": "30.0.0", "@types/react": "19.1.8", "@typescript-eslint/parser": "8.37.0", "babel-plugin-module-resolver": "5.0.2", "eslint": "8.57.1", "eslint-import-resolver-typescript": "4.4.4", "eslint-plugin-import-x": "4.16.1", "eslint-plugin-prettier": "5.5.1", "prettier": "3.6.2", "prettier-plugin-tailwindcss": "0.6.13", "tailwindcss": "3.4.17", "tailwindcss-animate": "1.0.7", "typescript": "5.8.3"}, "engines": {"node": ">=18"}}