import type { Theme } from '@react-navigation/native';
import { DarkTheme, DefaultTheme } from '@react-navigation/native';
import type { ClassValue } from 'clsx';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export const cn = (...inputs: ClassValue[]) => {
  return twMerge(clsx(inputs));
};

export const THEME = {
  light: {
    background: 'hsl(0 0% 100%)', // #ffffff
    foreground: 'hsl(0 0% 20%)', // #333333
    card: 'hsl(0 0% 100%)', // #ffffff
    cardForeground: 'hsl(0 0% 20%)', // #333333
    popover: 'hsl(0 0% 100%)', // #ffffff
    popoverForeground: 'hsl(0 0% 20%)', // #333333
    primary: 'hsl(217 91% 60%)', // #3b82f6
    primaryForeground: 'hsl(0 0% 100%)', // #ffffff
    secondary: 'hsl(220 14% 96%)', // #f3f4f6
    secondaryForeground: 'hsl(220 13% 33%)', // #4b5563
    muted: 'hsl(220 14% 98%)', // #f9fafb
    mutedForeground: 'hsl(220 9% 46%)', // #6b7280
    accent: 'hsl(199 89% 94%)', // #e0f2fe
    accentForeground: 'hsl(224 76% 33%)', // #1e3a8a
    destructive: 'hsl(0 84% 60%)', // #ef4444
    border: 'hsl(220 13% 91%)', // #e5e7eb
    input: 'hsl(220 13% 91%)', // #e5e7eb
    ring: 'hsl(217 91% 60%)', // #3b82f6
    radius: '0.375rem', // from global.css --radius
    chart1: 'hsl(217 91% 60%)', // primary blue
    chart2: 'hsl(224 76% 50%)', // darker blue
    chart3: 'hsl(214 95% 70%)', // lighter blue
    chart4: 'hsl(199 89% 60%)', // accent blue
    chart5: 'hsl(220 13% 60%)', // muted gray
  },
  dark: {
    background: 'hsl(0 0% 9%)', // #171717
    foreground: 'hsl(0 0% 90%)', // #e5e5e5
    card: 'hsl(0 0% 15%)', // #262626
    cardForeground: 'hsl(0 0% 90%)', // #e5e5e5
    popover: 'hsl(0 0% 15%)', // #262626
    popoverForeground: 'hsl(0 0% 90%)', // #e5e5e5
    primary: 'hsl(217 91% 60%)', // #3b82f6
    primaryForeground: 'hsl(0 0% 100%)', // #ffffff
    secondary: 'hsl(0 0% 15%)', // #262626
    secondaryForeground: 'hsl(0 0% 90%)', // #e5e5e5
    muted: 'hsl(0 0% 15%)', // #262626
    mutedForeground: 'hsl(0 0% 64%)', // #a3a3a3
    accent: 'hsl(224 76% 33%)', // #1e3a8a
    accentForeground: 'hsl(214 95% 87%)', // #bfdbfe
    destructive: 'hsl(0 84% 60%)', // #ef4444
    border: 'hsl(0 0% 25%)', // #404040
    input: 'hsl(0 0% 25%)', // #404040
    ring: 'hsl(217 91% 60%)', // #3b82f6
    radius: '0.375rem',
    chart1: 'hsl(217 91% 70%)', // brighter blue for dark mode
    chart2: 'hsl(224 76% 60%)',
    chart3: 'hsl(214 95% 80%)',
    chart4: 'hsl(199 89% 70%)',
    chart5: 'hsl(220 13% 70%)',
  },
};

export const NAV_THEME: Record<'light' | 'dark', Theme> = {
  light: {
    ...DefaultTheme,
    colors: {
      background: THEME.light.background,
      border: THEME.light.border,
      card: THEME.light.card,
      notification: THEME.light.destructive,
      primary: THEME.light.primary,
      text: THEME.light.foreground,
    },
  },
  dark: {
    ...DarkTheme,
    colors: {
      background: THEME.dark.background,
      border: THEME.dark.border,
      card: THEME.dark.card,
      notification: THEME.dark.destructive,
      primary: THEME.dark.primary,
      text: THEME.dark.foreground,
    },
  },
};
