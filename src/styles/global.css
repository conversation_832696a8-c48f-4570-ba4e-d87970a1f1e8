@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: #ffffff;
    --foreground: #333333;
    --card: #ffffff;
    --card-foreground: #333333;
    --popover: #ffffff;
    --popover-foreground: #333333;
    --primary: #3b82f6;
    --primary-foreground: #ffffff;
    --secondary: #f3f4f6;
    --secondary-foreground: #4b5563;
    --muted: #f9fafb;
    --muted-foreground: #6b7280;
    --accent: #e0f2fe;
    --accent-foreground: #1e3a8a;
    --destructive: #ef4444;
    --destructive-foreground: #ffffff;
    --border: #e5e7eb;
    --input: #e5e7eb;
    --ring: #3b82f6;
    --chart-1: #3b82f6;
    --chart-2: #2563eb;
    --chart-3: #1d4ed8;
    --chart-4: #1e40af;
    --chart-5: #1e3a8a;
    --sidebar: #f9fafb;
    --sidebar-foreground: #333333;
    --sidebar-primary: #3b82f6;
    --sidebar-primary-foreground: #ffffff;
    --sidebar-accent: #e0f2fe;
    --sidebar-accent-foreground: #1e3a8a;
    --sidebar-border: #e5e7eb;
    --sidebar-ring: #3b82f6;
    --font-sans: Inter, sans-serif;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.375rem;
    --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
    --tracking-normal: 0em;
    --spacing: 0.25rem;
  }

  @media (prefers-color-scheme: dark) {
    .dark:root {
      --background: #171717;
      --foreground: #e5e5e5;
      --card: #262626;
      --card-foreground: #e5e5e5;
      --popover: #262626;
      --popover-foreground: #e5e5e5;
      --primary: #3b82f6;
      --primary-foreground: #ffffff;
      --secondary: #262626;
      --secondary-foreground: #e5e5e5;
      --muted: #262626;
      --muted-foreground: #a3a3a3;
      --accent: #1e3a8a;
      --accent-foreground: #bfdbfe;
      --destructive: #ef4444;
      --destructive-foreground: #ffffff;
      --border: #404040;
      --input: #404040;
      --ring: #3b82f6;
      --chart-1: #60a5fa;
      --chart-2: #3b82f6;
      --chart-3: #2563eb;
      --chart-4: #1d4ed8;
      --chart-5: #1e40af;
      --sidebar: #171717;
      --sidebar-foreground: #e5e5e5;
      --sidebar-primary: #3b82f6;
      --sidebar-primary-foreground: #ffffff;
      --sidebar-accent: #1e3a8a;
      --sidebar-accent-foreground: #bfdbfe;
      --sidebar-border: #404040;
      --sidebar-ring: #3b82f6;
      --font-sans: Inter, sans-serif;
      --font-serif: Source Serif 4, serif;
      --font-mono: JetBrains Mono, monospace;
      --radius: 0.375rem;
      --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
      --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
      --shadow-sm:
        0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
      --shadow:
        0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
      --shadow-md:
        0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
      --shadow-lg:
        0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
      --shadow-xl:
        0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
      --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
    }
  }
}
